'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { Loader2, Python } from 'lucide-react';

// Dynamic import with no SSR for Pyodide components
const PyodideWorkspaceContainer = dynamic(
  () => import('./pyodide-workspace-container').then(mod => ({ default: mod.PyodideWorkspaceContainer })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-6 max-w-md mx-auto p-8">
          <div className="relative">
            <Python className="h-16 w-16 mx-auto text-primary opacity-20" />
            <Loader2 className="h-8 w-8 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-spin text-primary" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Loading Python Workspace</h3>
            <p className="text-muted-foreground">
              Initializing your development environment...
            </p>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
      </div>
    )
  }
);

interface PyodideWorkspaceClientProps {
  workspaceId: string;
  userId: string;
  workspace: {
    id: string;
    name: string;
    type: string;
    project?: {
      id: string;
      name: string;
      organizationId: string;
    };
  };
  className?: string;
}

export function PyodideWorkspaceClient(props: PyodideWorkspaceClientProps) {
  return <PyodideWorkspaceContainer {...props} />;
}
