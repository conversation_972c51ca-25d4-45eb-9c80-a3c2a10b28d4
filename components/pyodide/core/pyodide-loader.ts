/**
 * Pyodide Dynamic Loader
 * Handles dynamic loading of Pyodide to prevent build-time issues
 */

import type { PyodideInterface } from 'pyodide';

export interface PyodideLoaderConfig {
  indexURL?: string;
  fullStdLib?: boolean;
  stdin?: (prompt: string) => string;
  stdout?: (text: string) => void;
  stderr?: (text: string) => void;
  jsglobals?: object;
}

class PyodideLoader {
  private static instance: PyodideLoader | null = null;
  private pyodidePromise: Promise<PyodideInterface> | null = null;
  private isLoading = false;

  static getInstance(): PyodideLoader {
    if (!PyodideLoader.instance) {
      PyodideLoader.instance = new PyodideLoader();
    }
    return PyodideLoader.instance;
  }

  /**
   * Load Pyodide dynamically
   */
  async loadPyodide(config: PyodideLoaderConfig = {}): Promise<PyodideInterface> {
    // Return existing promise if already loading
    if (this.pyodidePromise) {
      return this.pyodidePromise;
    }

    // Prevent multiple simultaneous loads
    if (this.isLoading) {
      throw new Error('Pyodide is already being loaded');
    }

    this.isLoading = true;

    try {
      this.pyodidePromise = this.loadPyodideInternal(config);
      const pyodide = await this.pyodidePromise;
      this.isLoading = false;
      return pyodide;
    } catch (error) {
      this.isLoading = false;
      this.pyodidePromise = null;
      throw error;
    }
  }

  /**
   * Internal method to load Pyodide
   */
  private async loadPyodideInternal(config: PyodideLoaderConfig): Promise<PyodideInterface> {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Pyodide can only be loaded in a browser environment');
    }

    try {
      // Dynamic import of Pyodide with error handling for SSR
      let loadPyodide: any;

      try {
        const pyodideModule = await import('pyodide');
        loadPyodide = pyodideModule.loadPyodide;
      } catch (importError) {
        // Fallback for cases where dynamic import fails
        console.warn('Failed to import pyodide module:', importError);
        throw new Error('Pyodide module could not be loaded. Make sure you are in a browser environment.');
      }

      if (!loadPyodide) {
        throw new Error('loadPyodide function not found in pyodide module');
      }

      // Load Pyodide with configuration
      const pyodide = await loadPyodide({
        indexURL: config.indexURL || 'https://cdn.jsdelivr.net/pyodide/v0.28.0/full/',
        fullStdLib: config.fullStdLib ?? false,
        stdin: config.stdin ? (() => config.stdin!('')) : undefined,
        stdout: config.stdout,
        stderr: config.stderr,
        jsglobals: config.jsglobals || globalThis,
      });

      return pyodide;
    } catch (error) {
      console.error('Failed to load Pyodide:', error);
      throw new Error(`Failed to load Pyodide: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if Pyodide is already loaded
   */
  isLoaded(): boolean {
    return this.pyodidePromise !== null && !this.isLoading;
  }

  /**
   * Get loaded Pyodide instance (throws if not loaded)
   */
  async getPyodide(): Promise<PyodideInterface> {
    if (!this.pyodidePromise) {
      throw new Error('Pyodide not loaded. Call loadPyodide() first.');
    }
    return this.pyodidePromise;
  }

  /**
   * Reset the loader (for testing or reloading)
   */
  reset(): void {
    this.pyodidePromise = null;
    this.isLoading = false;
  }
}

// Export singleton instance
export const pyodideLoader = PyodideLoader.getInstance();

// Export types
export type { PyodideInterface };
