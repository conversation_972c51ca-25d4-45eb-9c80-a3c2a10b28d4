/**
 * Global Pyodide Manager
 * Prevents multiple instances and manages global Pyodide state
 * Based on best practices from Type9's Medium article
 */

import { pyodideLoader, type PyodideInterface, type PyodideLoaderConfig } from './pyodide-loader';

declare global {
  interface Window {
    __PYODIDE_INSTANCE__?: PyodideInterface;
    __PYODIDE_LOADING__?: Promise<PyodideInterface>;
  }
}

class PyodideManager {
  private static instance: PyodideManager | null = null;

  static getInstance(): PyodideManager {
    if (!PyodideManager.instance) {
      PyodideManager.instance = new PyodideManager();
    }
    return PyodideManager.instance;
  }

  /**
   * Load Pyodide instance with global singleton management
   */
  async loadInstance(config?: PyodideLoaderConfig): Promise<PyodideInterface | undefined> {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('PyodideManager: Not in browser environment');
      return undefined;
    }

    // Return existing instance if available
    if (window.__PYODIDE_INSTANCE__) {
      return window.__PYODIDE_INSTANCE__;
    }

    // Return existing loading promise if in progress
    if (window.__PYODIDE_LOADING__) {
      return window.__PYODIDE_LOADING__;
    }

    // Start loading process
    window.__PYODIDE_LOADING__ = this.initializePyodide(config);

    try {
      const pyodide = await window.__PYODIDE_LOADING__;
      window.__PYODIDE_INSTANCE__ = pyodide;
      delete window.__PYODIDE_LOADING__;
      return pyodide;
    } catch (error) {
      delete window.__PYODIDE_LOADING__;
      throw error;
    }
  }

  /**
   * Get existing Pyodide instance without loading
   */
  getInstance(): PyodideInterface | undefined {
    if (typeof window === 'undefined') {
      return undefined;
    }
    return window.__PYODIDE_INSTANCE__;
  }

  /**
   * Check if Pyodide is currently loading
   */
  isLoading(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }
    return !!window.__PYODIDE_LOADING__;
  }

  /**
   * Check if Pyodide is loaded and ready
   */
  isLoaded(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }
    return !!window.__PYODIDE_INSTANCE__;
  }

  /**
   * Initialize Pyodide with packages
   */
  private async initializePyodide(config?: PyodideLoaderConfig): Promise<PyodideInterface> {
    try {
      const pyodide = await pyodideLoader.loadPyodide(config || {});
      
      // Set up basic Python environment
      await this.setupBasicEnvironment(pyodide);
      
      return pyodide;
    } catch (error) {
      console.error('Error initializing Pyodide:', error);
      throw error;
    }
  }

  /**
   * Set up basic Python environment
   */
  private async setupBasicEnvironment(pyodide: PyodideInterface): Promise<void> {
    try {
      // Install micropip for package management
      await pyodide.loadPackage('micropip');
      
      // Set up basic imports
      pyodide.runPython(`
        import sys
        import os
        import json
        from datetime import datetime
        print(f"Python {sys.version} on Pyodide")
      `);
    } catch (error) {
      console.warn('Failed to set up basic Python environment:', error);
      // Don't throw here as Pyodide is still usable
    }
  }

  /**
   * Load Python packages
   */
  async loadPackages(packages: string[]): Promise<void> {
    const pyodide = this.getInstance();
    if (!pyodide) {
      throw new Error('Pyodide not loaded. Call loadInstance() first.');
    }

    if (packages.length === 0) {
      return;
    }

    try {
      // Load packages using micropip
      const micropip = pyodide.pyimport('micropip');
      await micropip.install(packages);
      console.log(`Loaded packages: ${packages.join(', ')}`);
    } catch (error) {
      console.error('Failed to load packages:', error);
      throw error;
    }
  }

  /**
   * Reset the global instance (useful for testing or reloading)
   */
  reset(): void {
    if (typeof window !== 'undefined') {
      delete window.__PYODIDE_INSTANCE__;
      delete window.__PYODIDE_LOADING__;
    }
  }
}

// Export singleton instance
export const pyodideManager = PyodideManager.getInstance();

// Export types
export type { PyodideInterface };
