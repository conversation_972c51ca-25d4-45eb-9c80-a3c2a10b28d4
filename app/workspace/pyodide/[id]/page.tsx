import { Metadata } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import dynamic from "next/dynamic";

// Dynamically import Pyodide components to prevent SSR issues
const PyodideWorkspaceContainer = dynamic(
  () => import("@/components/pyodide/workspace/pyodide-workspace-container").then(mod => ({ default: mod.PyodideWorkspaceContainer })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div>
            <h3 className="text-lg font-semibold">Loading Python Workspace</h3>
            <p className="text-muted-foreground">Preparing your development environment...</p>
          </div>
        </div>
      </div>
    )
  }
);

export const metadata: Metadata = {
  title: "Python Workspace",
  description: "Python development environment powered by Pyodide",
};

interface PyodideWorkspacePageProps {
  params: {
    id: string;
  };
}

export default async function PyodideWorkspacePage({ params }: PyodideWorkspacePageProps) {
  const session = await auth();

  if (!session?.user?.id) {
    redirect("/auth/login");
  }

  // Get workspace and verify access
  const workspace = await db.workspace.findUnique({
    where: { id: params.id },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          organizationId: true
        }
      }
    }
  });

  if (!workspace) {
    redirect("/dashboard");
  }

  // Verify workspace type
  if (workspace.type !== 'PYODIDE') {
    redirect(`/workspace/${params.id}`);
  }

  // TODO: Add proper access control check
  // For now, just check if user has access to the organization
  // In a real implementation, you'd check workspace permissions

  return (
    <PyodideWorkspaceContainer
      workspaceId={params.id}
      userId={session.user.id}
      workspace={workspace}
    />
  );
}
