import { redirect } from "next/navigation";
import { auth } from "@/auth";
import { db } from "@/lib/db";
import { PyodideWorkspaceContainer } from "@/components/pyodide/workspace/pyodide-workspace-container";

interface PyodideWorkspacePageProps {
  searchParams: {
    workspaceId?: string;
    projectId?: string;
  };
}

export default async function PyodideWorkspacePage({ 
  searchParams 
}: PyodideWorkspacePageProps) {
  const session = await auth();
  
  if (!session?.user?.id) {
    redirect("/auth/login");
  }

  const { workspaceId, projectId } = searchParams;

  // If workspaceId is provided, load that workspace
  if (workspaceId) {
    const workspace = await db.workspace.findUnique({
      where: { id: workspaceId },
      include: {
        project: {
          include: {
            organization: true,
          },
        },
        files: true,
      },
    });

    if (!workspace) {
      redirect("/dashboard/projects");
    }

    if (workspace.type !== "PYODIDE") {
      redirect(`/workspace?workspaceId=${workspaceId}`);
    }

    return (
      <PyodideWorkspaceContainer
        workspaceId={workspace.id}
        userId={session.user.id}
        workspace={{
          id: workspace.id,
          name: workspace.name,
          type: workspace.type,
          project: workspace.project ? {
            id: workspace.project.id,
            name: workspace.project.name,
            organizationId: workspace.project.organization.id,
          } : undefined,
        }}
      />
    );
  }

  // If projectId is provided, find or create a Pyodide workspace for that project
  if (projectId) {
    const project = await db.project.findUnique({
      where: { id: projectId },
      include: {
        workspaces: {
          where: { type: "PYODIDE" },
          include: {
            files: true,
          },
        },
        organization: true,
      },
    });

    if (!project) {
      redirect("/dashboard/projects");
    }

    // If project has a Pyodide workspace, use it
    if (project.workspaces.length > 0) {
      const workspace = project.workspaces[0];
      return (
        <PyodideWorkspaceContainer
          workspaceId={workspace.id}
          userId={session.user.id}
          workspace={{
            id: workspace.id,
            name: workspace.name,
            type: workspace.type,
            project: {
              id: project.id,
              name: project.name,
              organizationId: project.organization.id,
            },
          }}
        />
      );
    }

    // If no Pyodide workspace exists, create one
    const newWorkspace = await db.workspace.create({
      data: {
        name: `${project.name} - Pyodide`,
        type: "PYODIDE",
        status: "INACTIVE",
        projectId: project.id,
        config: {
          // Copy config from project if it has Pyodide-specific config
          ...(project.workspaces[0]?.config || {}),
          runtime: "pyodide",
          packages: ["numpy", "pandas", "matplotlib"],
        },
      },
      include: {
        project: {
          include: {
            organization: true,
          },
        },
        files: true,
      },
    });

    return (
      <PyodideWorkspaceContainer
        workspaceId={newWorkspace.id}
        userId={session.user.id}
        workspace={{
          id: newWorkspace.id,
          name: newWorkspace.name,
          type: newWorkspace.type,
          project: newWorkspace.project ? {
            id: newWorkspace.project.id,
            name: newWorkspace.project.name,
            organizationId: newWorkspace.project.organization.id,
          } : undefined,
        }}
      />
    );
  }

  // If neither workspaceId nor projectId is provided, redirect to projects
  redirect("/dashboard/projects");
}
